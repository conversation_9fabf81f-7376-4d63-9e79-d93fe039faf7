/**
 * 数值格式化工具函数
 * 用于处理金额显示格式化
 */

/**
 * 格式化金额显示
 * @param amount 金额（万元）
 * @param precision 小数位数，默认为2
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number, precision: number = 2): string {
  if (amount === 0) {
    return '0万元'
  }

  // 如果金额超过1万万元（即1亿元），转换为亿元显示
  if (amount >= 10000) {
    const amountInYi = amount / 10000
    return `${amountInYi.toFixed(precision)}亿元`
  }

  // 否则显示为万元
  return `${amount.toFixed(precision)}万元`
}

/**
 * 格式化金额显示（不带单位）
 * @param amount 金额（万元）
 * @param precision 小数位数，默认为2
 * @returns 格式化后的金额数字字符串
 */
export function formatAmountValue(amount: number, precision: number = 2): string {
  if (amount === 0) {
    return '0'
  }

  // 如果金额超过1万万元（即1亿元），转换为亿元显示
  if (amount >= 10000) {
    const amountInYi = amount / 10000
    return amountInYi.toFixed(precision)
  }

  // 否则显示为万元
  return amount.toFixed(precision)
}

/**
 * 获取金额单位
 * @param amount 金额（万元）
 * @returns 单位字符串
 */
export function getAmountUnit(amount: number): string {
  if (amount >= 10000) {
    return '亿元'
  }
  return '万元'
}

/**
 * 格式化数字，添加千分位分隔符
 * @param num 数字
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN')
}

/**
 * 格式化百分比
 * @param value 数值
 * @param precision 小数位数，默认为1
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, precision: number = 1): string {
  return `${value.toFixed(precision)}%`
}
