<script setup lang="ts">
import { computed, onMounted, ref, watch, watchEffect } from 'vue'

interface Props {
  /** 自动播放 */
  autoplay?: boolean
  /** 文字颜色 */
  color?: string
  /** 小数点符号 */
  decimal?: string
  /** 小数位数 */
  decimals?: number
  /** 动画持续时间(ms) */
  duration?: number
  /** 结束值 */
  endVal?: number
  /** 前缀 */
  prefix?: string
  /** 千分位分隔符 */
  separator?: string
  /** 开始值 */
  startVal?: number
  /** 后缀 */
  suffix?: string
  /** 是否使用缓动动画 */
  useEasing?: boolean
}

defineOptions({ name: 'CountToAnimator' })

const props = withDefaults(defineProps<Props>(), {
  autoplay: true,
  color: '',
  decimal: '.',
  decimals: 0,
  duration: 1500,
  endVal: 0,
  prefix: '',
  separator: ',',
  startVal: 0,
  suffix: '',
  useEasing: true,
})

const emit = defineEmits<{
  started: []
  finished: []
}>()

// 当前显示的数值
const displayValue = ref(props.startVal)
// 动画状态
const isAnimating = ref(false)
// 动画定时器
let animationFrame: number | null = null
let startTime: number | null = null

// 格式化显示的数值
const formattedValue = computed(() => formatNumber(displayValue.value))

// 缓动函数 - easeOutQuart
const easeOutQuart = (t: number): number => {
  return 1 - Math.pow(1 - t, 4)
}

// 线性函数
const linear = (t: number): number => {
  return t
}

// 格式化数字
function formatNumber(num: number): string {
  if (!num && num !== 0) {
    return ''
  }
  
  const { decimal, decimals, prefix, separator, suffix } = props
  let numStr = Number(num).toFixed(decimals)
  
  const parts = numStr.split('.')
  let integerPart = parts[0]
  const decimalPart = parts.length > 1 ? decimal + parts[1] : ''
  
  // 添加千分位分隔符
  if (separator && integerPart) {
    const rgx = /(\d+)(\d{3})/
    while (rgx.test(integerPart)) {
      integerPart = integerPart.replace(rgx, `$1${separator}$2`)
    }
  }
  
  return prefix + integerPart + decimalPart + suffix
}

// 执行动画
function animate(timestamp: number) {
  if (!startTime) {
    startTime = timestamp
    emit('started')
  }
  
  const elapsed = timestamp - startTime
  const progress = Math.min(elapsed / props.duration, 1)
  
  // 应用缓动函数
  const easedProgress = props.useEasing ? easeOutQuart(progress) : linear(progress)
  
  // 计算当前值
  const currentValue = props.startVal + (props.endVal - props.startVal) * easedProgress
  displayValue.value = currentValue
  
  if (progress < 1) {
    animationFrame = requestAnimationFrame(animate)
  } else {
    // 动画完成
    displayValue.value = props.endVal
    isAnimating.value = false
    startTime = null
    emit('finished')
  }
}

// 开始动画
function start() {
  if (isAnimating.value) {
    stop()
  }
  
  displayValue.value = props.startVal
  isAnimating.value = true
  startTime = null
  animationFrame = requestAnimationFrame(animate)
}

// 停止动画
function stop() {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
    animationFrame = null
  }
  isAnimating.value = false
  startTime = null
}

// 重置
function reset() {
  stop()
  displayValue.value = props.startVal
}

// 监听props变化
watchEffect(() => {
  displayValue.value = props.startVal
})

watch([() => props.startVal, () => props.endVal], () => {
  if (props.autoplay) {
    start()
  }
})

// 组件挂载时自动播放
onMounted(() => {
  if (props.autoplay) {
    start()
  }
})

// 暴露方法
defineExpose({ start, stop, reset })
</script>

<template>
  <span :style="{ color }" class="count-to-animator">
    {{ formattedValue }}
  </span>
</template>

<style scoped>
.count-to-animator {
  display: inline-block;
  font-variant-numeric: tabular-nums;
}
</style>
