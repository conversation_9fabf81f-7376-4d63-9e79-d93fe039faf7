<template>
  <div class="bg-yellow-50 overflow-hidden">
    <div class="flex items-center py-2 px-4">
      <!-- 滚动文字容器 -->
      <div class="flex-1 overflow-hidden">
        <div
          class="whitespace-nowrap text-yellow-800 text-sm font-medium animate-scroll flex items-center"
          :style="{ animationDuration: animationDuration }"
        >
          <!-- 小喇叭图标跟随文字滚动 -->
          <svg class="w-4 h-4 text-yellow-600 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h3.763l7.79 3.894A1 1 0 0018 15V3zM3.5 9.5a.5.5 0 01-.5-.5V8a.5.5 0 011 0v1a.5.5 0 01-.5.5zm2 2a.5.5 0 01-.5-.5v-3a.5.5 0 011 0v3a.5.5 0 01-.5.5zm2 2a.5.5 0 01-.5-.5v-7a.5.5 0 011 0v7a.5.5 0 01-.5.5z" clip-rule="evenodd" />
          </svg>
          <span>{{ noticeText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getContactSettings } from '../../services/contactService'

// 通知文字
const noticeText = ref('欢迎联系我们，我们将竭诚为您服务')

// 根据文字长度动态计算动画时长
const animationDuration = computed(() => {
  const textLength = noticeText.value.length
  // 基础时长20秒，每个字符增加0.5秒，让滚动更加缓慢平稳
  const duration = Math.max(20, textLength * 0.5)
  return `${duration}s`
})

// 加载联系方式设置
const loadContactSettings = async () => {
  try {
    const settings = await getContactSettings()
    if (settings && settings.contactText) {
      noticeText.value = settings.contactText
    }
  } catch (error) {
    console.error('获取联系方式设置失败:', error)
  }
}

// 组件挂载时加载设置
onMounted(() => {
  loadContactSettings()
})
</script>

<style scoped>
@keyframes scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-scroll {
  animation: scroll linear infinite;
}
</style>
